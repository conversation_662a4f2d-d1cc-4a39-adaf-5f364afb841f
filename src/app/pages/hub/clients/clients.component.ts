import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { TenantFormService } from './tenant-form.service';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Tenant } from './tenant.model';
import { MessageService } from 'primeng/api';
import { finalize } from 'rxjs/operators';

interface Office {
  id: string;
  name: string;
  branchId: string;
  status: string;
}

@Component({
  selector: 'app-clients',
  templateUrl: './clients.component.html',
  styleUrls: ['./clients.component.scss'],
  providers: [MessageService],
})
export class ClientsComponent implements OnInit {
  @Input() selectedOffice?: Office;
  @Output() formNext = new EventEmitter<any>();
  @Output() draftSaved = new EventEmitter<any>();

  tenants: Tenant[] = [];
  buildings: any[] = [];
  filteredTenants: Tenant[] = [];
  searchText: string = '';
  displayDialog: boolean = false;
  currentStep: number = 0;
  tenantForm: FormGroup;
  selectedTenant: Tenant | null = null;
  loading: boolean = false;
  savingDraft: boolean = false;
  availableOffices: Office[] = [];

  steps = [
    { label: 'Basic Info', icon: 'pi pi-user' },
    { label: 'Lease Details', icon: 'pi pi-file' },
    { label: 'Additional Services', icon: 'pi pi-cog' },
    { label: 'Review', icon: 'pi pi-check' },
  ];

  constructor(
    private fb: FormBuilder,
    private tenantFormService: TenantFormService,
    private messageService: MessageService
  ) {
    this.tenantForm = this.tenantFormService.createTenantForm();
  }

  ngOnInit(): void {
    this.loadTenants();
    this.loadAvailableOffices();
    this.loadExistingDraft();

    if (this.selectedOffice) {
      const officeControl = this.tenantForm.get(
        'leaseDetails.property.officeId'
      );
      if (officeControl) {
        officeControl.setValue(this.selectedOffice.id);
      }
    }
  }

  loadExistingDraft(): void {
    // If there's a draft in progress, load it
    const draftId = localStorage.getItem('current-draft-id');
    if (draftId) {
      this.tenantFormService.getDraft(draftId).subscribe((draft) => {
        if (draft) {
          this.tenantForm = this.tenantFormService.createTenantForm(draft);
          this.messageService.add({
            severity: 'info',
            summary: 'Draft Loaded',
            detail: 'Your previous draft has been restored',
          });
        }
      });
    }
  }

  loadAvailableOffices(): void {
    // Simulated API call - replace with actual service call
    this.availableOffices = [
      { id: '1', name: 'Office 101', branchId: '1', status: 'available' },
      { id: '2', name: 'Office 102', branchId: '1', status: 'available' },
      { id: '3', name: 'Office 201', branchId: '2', status: 'available' },
    ];
  }

  getFilteredOffices(branchId: string): Office[] {
    return this.availableOffices.filter(
      (office) => office.branchId === branchId && office.status === 'available'
    );
  }

  getBuildingName(buildingId: string): string {
    const building = this.buildings.find((b) => b.id === buildingId);
    return building?.name || '-';
  }

  loadTenants(): void {
    this.loading = true;
    // Simulate API call
    setTimeout(() => {
      this.tenants = [];
      this.buildings = [
        { id: '1', name: 'Building A' },
        { id: '2', name: 'Building B' },
        { id: '3', name: 'Building C' },
      ];
      this.filteredTenants = [...this.tenants];
      this.loading = false;
    }, 1000);
  }

  filterTenants(): void {
    if (!this.searchText) {
      this.filteredTenants = [...this.tenants];
      return;
    }

    this.filteredTenants = this.tenants.filter(
      (tenant) =>
        tenant.basicInfo.fullName
          .toLowerCase()
          .includes(this.searchText.toLowerCase()) ||
        tenant.basicInfo.email
          .toLowerCase()
          .includes(this.searchText.toLowerCase()) ||
        tenant.basicInfo.companyName
          ?.toLowerCase()
          .includes(this.searchText.toLowerCase())
    );
  }

  openNew(): void {
    console.log('Opening new tenant form');
    this.selectedTenant = null;
    this.tenantForm = this.tenantFormService.createTenantForm();
    this.currentStep = 0;
    this.displayDialog = true;
    console.log('Dialog should be visible:', this.displayDialog);
  }

  openEdit(tenant: Tenant): void {
    this.selectedTenant = tenant;
    this.tenantForm = this.tenantFormService.createTenantForm(tenant);
    this.currentStep = 0;
    this.displayDialog = true;
  }

  canProceedToNextStep(): boolean {
    switch (this.currentStep) {
      case 0:
        return this.tenantForm.get('basicInfo')?.valid ?? false;
      case 1:
        return this.tenantForm.get('leaseDetails')?.valid ?? false;
      case 2:
        return true; // Additional services are optional
      case 3:
        return this.tenantForm.valid;
      default:
        return false;
    }
  }

  saveAsDraft(): void {
    if (this.savingDraft) {
      return;
    }

    this.savingDraft = true;
    const formData = this.tenantForm.getRawValue();
    formData.lastUpdated = new Date();

    this.tenantFormService
      .saveDraft(formData)
      .pipe(finalize(() => (this.savingDraft = false)))
      .subscribe(
        (response) => {
          // Store the draft ID for later use
          if (response.data?.draftId) {
            localStorage.setItem('current-draft-id', response.data.draftId);
          }

          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Form saved as draft',
          });

          this.draftSaved.emit(response.data);
        },
        (error) => {
          console.error('Error saving draft:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to save draft',
          });
        }
      );
  }

  nextStep(): void {
    if (this.canProceedToNextStep()) {
      if (this.currentStep < this.steps.length - 1) {
        this.formNext.emit(this.tenantForm.value);
        this.currentStep++;
      } else {
        this.saveTenant();
      }
    } else {
      this.messageService.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please fill all required fields before proceeding',
      });
      this.markFormGroupTouched(this.tenantForm);
    }
  }

  prevStep(): void {
    if (this.currentStep > 0) {
      this.currentStep--;
    }
  }

  // Helper method to show validation errors
  private markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else {
        control.markAsTouched();
      }
    });
  }

  saveTenant(): void {
    if (this.tenantForm.invalid) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please fill all required fields',
      });
      this.markFormGroupTouched(this.tenantForm);
      return;
    }

    const tenantData = this.tenantForm.value;
    if (this.selectedTenant) {
      // Update existing tenant
      const index = this.tenants.findIndex(
        (t) => t.id === this.selectedTenant?.id
      );
      if (index !== -1) {
        this.tenants[index] = { ...tenantData, id: this.selectedTenant.id };
      }
    } else {
      // Add new tenant
      tenantData.id = Math.random().toString(36).substring(2);
      this.tenants.unshift(tenantData);
    }

    this.filteredTenants = [...this.tenants];
    this.displayDialog = false;
    this.messageService.add({
      severity: 'success',
      summary: 'Success',
      detail: `Tenant ${
        this.selectedTenant ? 'updated' : 'created'
      } successfully`,
    });
  }
}
