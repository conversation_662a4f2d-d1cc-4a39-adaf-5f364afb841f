<!-- tenant-management.component.html -->
<div class="flex justify-content-between align-items-center mb-4">
  <h5>Tenant Management</h5>
  <div class="d-flex gap-2 justify-content-between">
    <span class="p-input-icon-left">
      <i class="pi pi-search"></i>
      <input
        type="text"
        pInputText
        [(ngModel)]="searchText"
        (input)="filterTenants()"
        placeholder="Search tenants..."
      />
    </span>
    <button
      pButton
      icon="pi pi-plus"
      label="New Tenant"
      (click)="openNew()"
    ></button>
  </div>
</div>

<p-table
  [value]="filteredTenants"
  [loading]="loading"
  [paginator]="true"
  [rows]="10"
  styleClass="p-datatable-sm"
>
  <ng-template pTemplate="header">
    <tr>
      <th>Name</th>
      <th>Company</th>
      <th>Email</th>
      <th>Phone</th>
      <th>Actions</th>
    </tr>
  </ng-template>
  <ng-template pTemplate="body" let-tenant>
    <tr>
      <td>{{ tenant.basicInfo.fullName }}</td>
      <td>{{ tenant.basicInfo.companyName || "-" }}</td>
      <td>{{ tenant.basicInfo.email }}</td>
      <td>{{ tenant.basicInfo.phone }}</td>
      <td>
        <button
          pButton
          icon="pi pi-pencil"
          class="p-button-rounded p-button-text"
          (click)="openEdit(tenant)"
        ></button>
      </td>
    </tr>
  </ng-template>
  <ng-template pTemplate="emptymessage">
    <tr>
      <td colspan="5">No tenants found</td>
    </tr>
  </ng-template>
</p-table>

<!-- Multi-step Dialog -->
<p-dialog
  [(visible)]="displayDialog"
  [modal]="true"
  [draggable]="false"
  [resizable]="false"
  [closable]="true"
  [dismissableMask]="true"
  [style]="{ width: '80vw', maxWidth: '1200px' }"
  [baseZIndex]="10000"
  [header]="(selectedTenant ? 'Edit' : 'Create') + ' Tenant'"
>
  <div class="form-container">
    <p-steps [model]="steps" [activeIndex]="currentStep"></p-steps>

    <div class="mt-4">
      <!-- Step 1: Basic Info -->
      <form *ngIf="currentStep === 0" [formGroup]="tenantForm">
        <div formGroupName="basicInfo" class="p-fluid grid">
          <div class="field col-12 md:col-6">
            <label for="fullName">Full Name*</label>
            <input
              id="fullName"
              type="text"
              pInputText
              formControlName="fullName"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  tenantForm.get('basicInfo.fullName')?.invalid &&
                  tenantForm.get('basicInfo.fullName')?.touched
              }"
            />
            <small
              class="p-error"
              *ngIf="
                tenantForm.get('basicInfo.fullName')?.invalid &&
                tenantForm.get('basicInfo.fullName')?.touched
              "
            >
              Full name is required
            </small>
          </div>

          <div class="field col-12 md:col-6">
            <label for="email">Email*</label>
            <input
              id="email"
              type="email"
              pInputText
              formControlName="email"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  tenantForm.get('basicInfo.email')?.invalid &&
                  tenantForm.get('basicInfo.email')?.touched
              }"
            />
            <small
              class="p-error"
              *ngIf="
                tenantForm.get('basicInfo.email')?.invalid &&
                tenantForm.get('basicInfo.email')?.touched
              "
            >
              Valid email is required
            </small>
          </div>

          <div class="field col-12 md:col-6">
            <label for="phone">Phone*</label>
            <input
              id="phone"
              type="tel"
              pInputText
              formControlName="phone"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  tenantForm.get('basicInfo.phone')?.invalid &&
                  tenantForm.get('basicInfo.phone')?.touched
              }"
            />
            <small
              class="p-error"
              *ngIf="
                tenantForm.get('basicInfo.phone')?.invalid &&
                tenantForm.get('basicInfo.phone')?.touched
              "
            >
              Phone number is required
            </small>
          </div>

          <div class="field col-12 md:col-6">
            <label for="companyName">Company Name</label>
            <input
              id="companyName"
              type="text"
              pInputText
              formControlName="companyName"
            />
          </div>

          <div class="field col-12 md:col-6">
            <label for="jobTitle">Job Title</label>
            <input
              id="jobTitle"
              type="text"
              pInputText
              formControlName="jobTitle"
            />
          </div>

          <div class="field col-12 md:col-6">
            <label for="idNumber">ID Number</label>
            <input
              id="idNumber"
              type="text"
              pInputText
              formControlName="idNumber"
            />
          </div>

          <div class="field col-12 md:col-6">
            <label for="taxId">Tax ID</label>
            <input id="taxId" type="text" pInputText formControlName="taxId" />
          </div>
        </div>

        <div formGroupName="leaseDetails" class="p-fluid grid mt-4">
          <div formGroupName="property" class="col-12">
            <div class="grid">
              <div class="field col-12 md:col-6">
                <label for="buildingId">Building*</label>
                <p-dropdown
                  id="buildingId"
                  formControlName="buildingId"
                  [options]="buildings"
                  optionLabel="name"
                  placeholder="Select Building"
                  [ngClass]="{
                    'ng-invalid ng-dirty':
                      tenantForm.get('leaseDetails.property.buildingId')
                        ?.invalid &&
                      tenantForm.get('leaseDetails.property.buildingId')
                        ?.touched
                  }"
                ></p-dropdown>
                <small
                  class="p-error"
                  *ngIf="
                    tenantForm.get('leaseDetails.property.buildingId')
                      ?.invalid &&
                    tenantForm.get('leaseDetails.property.buildingId')?.touched
                  "
                >
                  Building selection is required
                </small>
              </div>

              <div class="field col-12 md:col-6">
                <label for="officeId">Office*</label>
                <p-dropdown
                  id="officeId"
                  formControlName="officeId"
                  [options]="
                    getFilteredOffices(
                      tenantForm.get('leaseDetails.property.buildingId')?.value
                    )
                  "
                  optionLabel="name"
                  placeholder="Select Office"
                  [ngClass]="{
                    'ng-invalid ng-dirty':
                      tenantForm.get('leaseDetails.property.officeId')
                        ?.invalid &&
                      tenantForm.get('leaseDetails.property.officeId')?.touched
                  }"
                ></p-dropdown>
                <small
                  class="p-error"
                  *ngIf="
                    tenantForm.get('leaseDetails.property.officeId')?.invalid &&
                    tenantForm.get('leaseDetails.property.officeId')?.touched
                  "
                >
                  Office selection is required
                </small>
              </div>
            </div>
          </div>
        </div>
      </form>

      <!-- Step 2: Lease Details -->
      <form *ngIf="currentStep === 1" [formGroup]="tenantForm">
        <div formGroupName="leaseDetails" class="p-fluid grid">
          <div formGroupName="property" class="col-12">
            <div class="grid">
              <div class="field col-12 md:col-6">
                <label for="buildingId">Building*</label>
                <p-dropdown
                  id="buildingId"
                  formControlName="buildingId"
                  [options]="buildings"
                  optionLabel="name"
                  placeholder="Select Building"
                  [ngClass]="{
                    'ng-invalid ng-dirty':
                      tenantForm.get('leaseDetails.property.buildingId')
                        ?.invalid &&
                      tenantForm.get('leaseDetails.property.buildingId')
                        ?.touched
                  }"
                ></p-dropdown>
                <small
                  class="p-error"
                  *ngIf="
                    tenantForm.get('leaseDetails.property.buildingId')
                      ?.invalid &&
                    tenantForm.get('leaseDetails.property.buildingId')?.touched
                  "
                >
                  Building selection is required
                </small>
              </div>

              <div class="field col-12 md:col-6">
                <label for="officeId">Office*</label>
                <p-dropdown
                  id="officeId"
                  formControlName="officeId"
                  [options]="
                    getFilteredOffices(
                      tenantForm.get('leaseDetails.property.buildingId')?.value
                    )
                  "
                  optionLabel="name"
                  placeholder="Select Office"
                  [ngClass]="{
                    'ng-invalid ng-dirty':
                      tenantForm.get('leaseDetails.property.officeId')
                        ?.invalid &&
                      tenantForm.get('leaseDetails.property.officeId')?.touched
                  }"
                ></p-dropdown>
                <small
                  class="p-error"
                  *ngIf="
                    tenantForm.get('leaseDetails.property.officeId')?.invalid &&
                    tenantForm.get('leaseDetails.property.officeId')?.touched
                  "
                >
                  Office selection is required
                </small>
              </div>
            </div>
          </div>

          <div formGroupName="terms" class="col-12 mt-4">
            <h5>Lease Terms</h5>
            <div class="grid">
              <div class="field col-12 md:col-6">
                <label for="startDate">Start Date*</label>
                <p-calendar
                  id="startDate"
                  formControlName="startDate"
                  [showIcon]="true"
                  dateFormat="yy-mm-dd"
                  [ngClass]="{
                    'ng-invalid ng-dirty':
                      tenantForm.get('leaseDetails.terms.startDate')?.invalid &&
                      tenantForm.get('leaseDetails.terms.startDate')?.touched
                  }"
                ></p-calendar>
                <small
                  class="p-error"
                  *ngIf="
                    tenantForm.get('leaseDetails.terms.startDate')?.invalid &&
                    tenantForm.get('leaseDetails.terms.startDate')?.touched
                  "
                >
                  Start date is required
                </small>
              </div>

              <div class="field col-12 md:col-6">
                <label for="endDate">End Date*</label>
                <p-calendar
                  id="endDate"
                  formControlName="endDate"
                  [showIcon]="true"
                  dateFormat="yy-mm-dd"
                  [ngClass]="{
                    'ng-invalid ng-dirty':
                      tenantForm.get('leaseDetails.terms.endDate')?.invalid &&
                      tenantForm.get('leaseDetails.terms.endDate')?.touched
                  }"
                ></p-calendar>
                <small
                  class="p-error"
                  *ngIf="
                    tenantForm.get('leaseDetails.terms.endDate')?.invalid &&
                    tenantForm.get('leaseDetails.terms.endDate')?.touched
                  "
                >
                  End date is required
                </small>
              </div>
            </div>
          </div>
        </div>
      </form>

      <!-- Step 3: Additional Services -->
      <form *ngIf="currentStep === 2" [formGroup]="tenantForm">
        <div formGroupName="additionalServices" class="p-fluid grid">
          <div class="field col-12 md:col-4">
            <p-checkbox
              formControlName="internet"
              inputId="internet"
            ></p-checkbox>
            <label for="internet" class="ml-2">Internet Package</label>
          </div>
          <div class="field col-12 md:col-4">
            <p-checkbox
              formControlName="cleaning"
              inputId="cleaning"
            ></p-checkbox>
            <label for="cleaning" class="ml-2">Cleaning Service</label>
          </div>
          <div class="field col-12 md:col-4">
            <p-checkbox
              formControlName="meetingRoom"
              inputId="meetingRoom"
            ></p-checkbox>
            <label for="meetingRoom" class="ml-2">Meeting Room Access</label>
          </div>
          <div class="field col-12 md:col-4">
            <p-checkbox
              formControlName="parking"
              inputId="parking"
            ></p-checkbox>
            <label for="parking" class="ml-2">Parking Space</label>
          </div>
          <div class="field col-12 md:col-4">
            <p-checkbox
              formControlName="mailHandling"
              inputId="mailHandling"
            ></p-checkbox>
            <label for="mailHandling" class="ml-2">Mail Handling</label>
          </div>
        </div>
      </form>

      <!-- Step 4: Review -->
      <div *ngIf="currentStep === 3">
        <div class="review-section">
          <h4>Basic Information</h4>
          <div class="grid">
            <div class="col-12 md:col-6">
              <strong>Full Name:</strong>
              <div>{{ tenantForm.get("basicInfo.fullName")?.value }}</div>
            </div>
            <div class="col-12 md:col-6">
              <strong>Email:</strong>
              <div>{{ tenantForm.get("basicInfo.email")?.value }}</div>
            </div>
            <div class="col-12 md:col-6">
              <strong>Phone:</strong>
              <div>{{ tenantForm.get("basicInfo.phone")?.value }}</div>
            </div>
            <div
              class="col-12 md:col-6"
              *ngIf="tenantForm.get('basicInfo.companyName')?.value"
            >
              <strong>Company:</strong>
              <div>{{ tenantForm.get("basicInfo.companyName")?.value }}</div>
            </div>
          </div>

          <h4 class="mt-4">Lease Details</h4>
          <div class="grid">
            <div class="col-12 md:col-6">
              <strong>Building:</strong>
              <div>
                {{
                  getBuildingName(
                    tenantForm.get("leaseDetails.property.buildingId")?.value
                  )
                }}
              </div>
            </div>
            <div class="col-12 md:col-6">
              <strong>Floor:</strong>
              <div>
                {{
                  tenantForm.get("leaseDetails.property.floor")?.value || "-"
                }}
              </div>
            </div>
            <div class="col-12 md:col-6">
              <strong>Start Date:</strong>
              <div>
                {{
                  tenantForm.get("leaseDetails.terms.startDate")?.value | date
                }}
              </div>
            </div>
            <div class="col-12 md:col-6">
              <strong>End Date:</strong>
              <div>
                {{ tenantForm.get("leaseDetails.terms.endDate")?.value | date }}
              </div>
            </div>
          </div>

          <h4 class="mt-4">Additional Services</h4>
          <div class="grid">
            <div class="col-12">
              <ul class="list-none p-0 m-0">
                <li
                  *ngIf="tenantForm.get('additionalServices.internet')?.value"
                >
                  ✓ Internet Package
                </li>
                <li
                  *ngIf="tenantForm.get('additionalServices.cleaning')?.value"
                >
                  ✓ Cleaning Service
                </li>
                <li
                  *ngIf="
                    tenantForm.get('additionalServices.meetingRoom')?.value
                  "
                >
                  ✓ Meeting Room Access
                </li>
                <li *ngIf="tenantForm.get('additionalServices.parking')?.value">
                  ✓ Parking Space
                </li>
                <li
                  *ngIf="
                    tenantForm.get('additionalServices.mailHandling')?.value
                  "
                >
                  ✓ Mail Handling
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Dialog Footer with Navigation Buttons -->
  <ng-template pTemplate="footer">
    <div class="flex justify-content-between">
      <div>
        <button
          pButton
          type="button"
          label="Save as Draft"
          class="p-button-secondary mr-2"
          (click)="saveAsDraft()"
        ></button>
        <button
          pButton
          type="button"
          [label]="currentStep === 0 ? 'Cancel' : 'Previous'"
          class="p-button-secondary"
          (click)="currentStep === 0 ? (displayDialog = false) : prevStep()"
        ></button>
      </div>
      <button
        pButton
        type="button"
        [label]="currentStep === steps.length - 1 ? 'Save' : 'Next'"
        (click)="currentStep === steps.length - 1 ? saveTenant() : nextStep()"
        [disabled]="!canProceedToNextStep()"
      ></button>
    </div>
  </ng-template>
</p-dialog>

<!-- Toast for messages -->
<p-toast></p-toast>
