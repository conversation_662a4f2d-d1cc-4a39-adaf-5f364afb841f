export interface Tenant {
  id?: string; // For edits
  draftId?: string;
  basicInfo: {
    fullName: string;
    email: string;
    phone: string;
    companyName?: string;
    jobTitle?: string;
    idNumber?: string;
    taxId?: string;
  };
  contactPreferences: {
    preferredCommunication: 'Email' | 'Phone';
    emergencyContact: {
      name: string;
      phone: string;
    };
  };
  leaseDetails: {
    property: {
      buildingId: string; // Dropdown value
      officeId: string; // Added office ID
      floor: string;
      deskNumber?: string;
    };
    terms: {
      startDate: Date;
      endDate: Date;
      rentalType: 'Daily' | 'Weekly' | 'Monthly' | 'Yearly';
      rentAmount: number;
      currency: string;
      securityDeposit: number;
      paymentDueDay: number; // e.g., 1 for 1st of month
      lateFeePolicy: string;
    };
    paymentMethod: {
      type: 'Bank Transfer' | 'Credit Card' | 'Other';
      billingCycle: 'Monthly' | 'Quarterly';
      autoPay: boolean;
    };
  };
  additionalServices: {
    internet: boolean;
    cleaning: boolean;
    meetingRoom: boolean;
    parking: boolean;
    mailHandling: boolean;
  };
  legal: {
    agreementSigned: boolean;
    termsAccepted: boolean;
    privacyConsent: boolean;
  };
  adminNotes: {
    specialRequests?: string;
    moveInChecklist: string[];
  };
  status?: 'active' | 'draft' | 'inactive';
  lastUpdated?: Date;
}
