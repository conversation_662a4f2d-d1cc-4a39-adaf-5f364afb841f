import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { Tenant } from './tenant.model';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class TenantFormService {
  private apiUrl = environment.apiUrl + '/tenants'; // Update this with your actual API URL

  constructor(private fb: FormBuilder, private http: HttpClient) {}

  createTenantForm(tenant?: Tenant): FormGroup {
    const form = this.fb.group({
      basicInfo: this.fb.group({
        fullName: [tenant?.basicInfo.fullName || '', Validators.required],
        email: [
          tenant?.basicInfo.email || '',
          [Validators.required, Validators.email],
        ],
        phone: [tenant?.basicInfo.phone || '', Validators.required],
        companyName: [tenant?.basicInfo.companyName || ''],
        jobTitle: [tenant?.basicInfo.jobTitle || ''],
        idNumber: [tenant?.basicInfo.idNumber || ''],
        taxId: [tenant?.basicInfo.taxId || ''],
      }),
      contactPreferences: this.fb.group({
        preferredCommunication: [
          tenant?.contactPreferences.preferredCommunication || 'Email',
        ],
        emergencyContact: this.fb.group({
          name: [tenant?.contactPreferences.emergencyContact.name || ''],
          phone: [tenant?.contactPreferences.emergencyContact.phone || ''],
        }),
      }),
      leaseDetails: this.fb.group({
        property: this.fb.group({
          buildingId: [
            tenant?.leaseDetails.property.buildingId || '',
            Validators.required,
          ],
          officeId: [
            tenant?.leaseDetails.property.officeId || '',
            Validators.required,
          ],
          floor: [tenant?.leaseDetails.property.floor || ''],
          deskNumber: [tenant?.leaseDetails.property.deskNumber || ''],
        }),
        terms: this.fb.group({
          startDate: [
            tenant?.leaseDetails.terms.startDate || '',
            Validators.required,
          ],
          endDate: [
            tenant?.leaseDetails.terms.endDate || '',
            Validators.required,
          ],
          rentalType: [tenant?.leaseDetails.terms.rentalType || 'Monthly'],
          rentAmount: [
            tenant?.leaseDetails.terms.rentAmount || 0,
            [Validators.required, Validators.min(1)],
          ],
          currency: [tenant?.leaseDetails.terms.currency || 'USD'],
          securityDeposit: [tenant?.leaseDetails.terms.securityDeposit || 0],
          paymentDueDay: [tenant?.leaseDetails.terms.paymentDueDay || 1],
          lateFeePolicy: [tenant?.leaseDetails.terms.lateFeePolicy || ''],
        }),
        paymentMethod: this.fb.group({
          type: [tenant?.leaseDetails.paymentMethod.type || 'Bank Transfer'],
          billingCycle: [
            tenant?.leaseDetails.paymentMethod.billingCycle || 'Monthly',
          ],
          autoPay: [tenant?.leaseDetails.paymentMethod.autoPay || false],
        }),
      }),
      additionalServices: this.fb.group({
        internet: [tenant?.additionalServices.internet || false],
        cleaning: [tenant?.additionalServices.cleaning || false],
        meetingRoom: [tenant?.additionalServices.meetingRoom || false],
        parking: [tenant?.additionalServices.parking || false],
        mailHandling: [tenant?.additionalServices.mailHandling || false],
      }),
      legal: this.fb.group({
        agreementSigned: [
          tenant?.legal.agreementSigned || false,
          Validators.requiredTrue,
        ],
        termsAccepted: [
          tenant?.legal.termsAccepted || false,
          Validators.requiredTrue,
        ],
        privacyConsent: [
          tenant?.legal.privacyConsent || false,
          Validators.requiredTrue,
        ],
      }),
      adminNotes: this.fb.group({
        specialRequests: [tenant?.adminNotes.specialRequests || ''],
        moveInChecklist: this.fb.array(
          tenant?.adminNotes.moveInChecklist
            ? tenant.adminNotes.moveInChecklist.map((item) =>
                this.fb.control(item)
              )
            : []
        ),
      }),
      status: [tenant?.status || 'active'],
      draftId: [tenant?.draftId || null], // Add draftId to track drafts
    });

    return form;
  }

  saveDraft(formData: any): Observable<any> {
    // If we're in development mode without a backend, simulate the save
    if (!environment.production) {
      const draftId = formData.draftId || Date.now().toString();
      const draft = {
        ...formData,
        draftId,
        status: 'draft',
        lastUpdated: new Date(),
      };

      // Store in localStorage for persistence
      localStorage.setItem(`tenant-draft-${draftId}`, JSON.stringify(draft));

      return of({ success: true, data: draft });
    }

    // If we have a backend, use the real API
    return this.http.post(`${this.apiUrl}/drafts`, {
      ...formData,
      status: 'draft',
    });
  }

  getDraft(draftId: string): Observable<any> {
    // If we're in development mode without a backend, get from localStorage
    if (!environment.production) {
      const draft = localStorage.getItem(`tenant-draft-${draftId}`);
      return of(draft ? JSON.parse(draft) : null);
    }

    // If we have a backend, use the real API
    return this.http.get(`${this.apiUrl}/drafts/${draftId}`);
  }

  getAllDrafts(): Observable<any[]> {
    // If we're in development mode without a backend, get all from localStorage
    if (!environment.production) {
      const drafts = Object.keys(localStorage)
        .filter((key) => key.startsWith('tenant-draft-'))
        .map((key) => JSON.parse(localStorage.getItem(key) || '{}'))
        .sort(
          (a, b) =>
            new Date(b.lastUpdated).getTime() -
            new Date(a.lastUpdated).getTime()
        );

      return of(drafts);
    }

    // If we have a backend, use the real API
    return this.http.get<any[]>(`${this.apiUrl}/drafts`);
  }

  deleteDraft(draftId: string): Observable<any> {
    // If we're in development mode without a backend, remove from localStorage
    if (!environment.production) {
      localStorage.removeItem(`tenant-draft-${draftId}`);
      return of({ success: true });
    }

    // If we have a backend, use the real API
    return this.http.delete(`${this.apiUrl}/drafts/${draftId}`);
  }

  // ... rest of the service methods ...
}
